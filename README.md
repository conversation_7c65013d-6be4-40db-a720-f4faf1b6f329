# DropShelf - Windows Productivity Tool

A floating shelf that can be summoned by shaking the mouse while dragging files. Provides temporary storage for files that can be dragged to other locations.

## Features

- **Floating Shelf Window**: Frameless, always-on-top, semi-transparent window
- **Shake-to-Summon**: Gesture detection using mouse shake while dragging files
- **Drag-and-Drop Support**: Drop files onto the shelf and drag them out to other locations
- **Auto-Hide**: Automatically hides when losing focus or after inactivity
- **Dynamic Sizing**: Window size adjusts based on content

## Technology Stack

- **Python 3.10+**
- **PyQt6** - Modern GUI framework
- **pynput** - Global input monitoring for gesture detection
- **Pillow** - Image processing for thumbnails
- **pywin32** - Windows-specific API calls (optional)

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```bash
python main.py
```

### Current Implementation Status

**✅ Step 1 Complete: Basic Shelf Window**
- Frameless, semi-transparent PyQt6 window
- Always-on-top behavior
- Modern styling with gradient background and drop shadow
- Auto-hide functionality with timer
- Drag-and-drop reception (basic implementation)

**✅ Step 2 Complete: Enhanced Drag-and-Drop**
- Draggable file items with visual feedback
- Support for single and multi-file drag operations
- File type icons and tooltips
- Selection system with Ctrl+click support

**✅ Step 3 Complete: Mouse Shake Gesture Detection**
- Real-time mouse movement monitoring using pynput
- Configurable shake detection sensitivity
- Drag state detection using Windows API
- Background thread processing with Qt signals

**✅ Step 4 Complete: Full Integration**
- Gesture detection triggers shelf display
- Shelf appears near cursor when shake detected
- Files can be dragged in and out of shelf
- Complete workflow: drag file → shake mouse → shelf appears → drop files

## Project Structure

```
dropshelf/
├── main.py             # Application entry point
├── shelf_widget.py     # Core Shelf UI window class
├── gesture_detector.py # Mouse shake detection system
├── item_widget.py      # Draggable file item components
├── test_gesture.py     # Gesture detection testing tool
├── run_dropshelf.py    # Enhanced launcher with options
├── requirements.txt    # Python dependencies
├── test_file.txt       # Sample file for testing
└── README.md          # This file
```

## Usage Instructions

### Basic Usage
1. **Start the application**:
   ```bash
   python main.py
   ```

2. **Summon the shelf**:
   - Drag any file from File Explorer
   - While dragging, quickly shake your mouse left-right-left
   - The shelf will appear near your cursor

3. **Use the shelf**:
   - Drop files onto the shelf to store them temporarily
   - Drag files out of the shelf to other locations
   - Press Esc to hide the shelf manually
   - Shelf auto-hides after 5 seconds of inactivity

### Testing and Debugging

1. **Test gesture detection**:
   ```bash
   python test_gesture.py
   ```
   This opens a test interface where you can:
   - Adjust shake sensitivity parameters
   - Monitor detection events in real-time
   - Fine-tune the gesture recognition

2. **Debug mode**:
   ```bash
   python run_dropshelf.py --debug
   ```
   - Extended auto-hide timer (30 seconds)
   - Detailed console output

3. **Disable auto-hide**:
   ```bash
   python run_dropshelf.py --no-auto-hide
   ```

## Development Notes

- Uses proper PyQt6 syntax (not PyQt5)
- Includes comprehensive error handling
- Follows Python best practices and PEP 8 style guidelines
- Modular design for easy extension and maintenance
