# DropShelf - Windows Productivity Tool

A floating shelf that can be summoned by shaking the mouse while dragging files. Provides temporary storage for files that can be dragged to other locations.

## Features

- **Floating Shelf Window**: Frameless, always-on-top, semi-transparent window
- **Shake-to-Summon**: Gesture detection using mouse shake while dragging files
- **Drag-and-Drop Support**: Drop files onto the shelf and drag them out to other locations
- **Auto-Hide**: Automatically hides when losing focus or after inactivity
- **Dynamic Sizing**: Window size adjusts based on content

## Technology Stack

- **Python 3.10+**
- **PyQt6** - Modern GUI framework
- **pynput** - Global input monitoring for gesture detection
- **Pillow** - Image processing for thumbnails
- **pywin32** - Windows-specific API calls (optional)

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```bash
python main.py
```

### Current Implementation Status

**✅ Step 1 Complete: Basic Shelf Window**
- Frameless, semi-transparent PyQt6 window
- Always-on-top behavior
- Modern styling with gradient background and drop shadow
- Auto-hide functionality with timer
- Drag-and-drop reception (basic implementation)

**🚧 Next Steps:**
- Step 2: Enhanced drag-and-drop with file type detection
- Step 3: Mouse shake gesture detection
- Step 4: Integration of gesture detection with window display

## Project Structure

```
dropshelf/
├── main.py             # Application entry point
├── shelf_widget.py     # Core Shelf UI window class
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Testing

The current implementation shows the shelf window at the center of the screen for testing purposes. You can:

1. **Test Window Appearance**: The shelf appears as a semi-transparent, rounded window
2. **Test Drag-and-Drop**: Drag files from File Explorer onto the shelf
3. **Test Auto-Hide**: The shelf will auto-hide after 10 seconds (testing timer)
4. **Test Manual Hide**: Press Esc key to hide the shelf manually

## Development Notes

- Uses proper PyQt6 syntax (not PyQt5)
- Includes comprehensive error handling
- Follows Python best practices and PEP 8 style guidelines
- Modular design for easy extension and maintenance
