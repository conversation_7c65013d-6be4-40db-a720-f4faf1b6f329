#!/usr/bin/env python3
"""
改进版本的DropShelf主应用程序
实现所有用户要求的功能：
1. 相同文件不能重复拖进去
2. 统一展示为一个图标，显示文件数量
3. 点击图标展开/收起文件列表
4. 只在拖动文件时检测抖动，窗口显示时停止检测
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# 导入组件
from improved_shelf_widget import ImprovedShelfWidget
from gesture_detector import GestureDetector


class ImprovedDropShelfApp:
    """改进版本的DropShelf应用程序"""
    
    def __init__(self):
        self.app = None
        self.shelf_widget = None
        self.gesture_detector = None
        self.shelf_visible = False
        
    def setup_application(self):
        """设置应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("DropShelf Improved")
        self.app.setApplicationVersion("2.0.0")
        self.app.setOrganizationName("DropShelf")
        print("✅ 改进版应用程序设置完成")
        
    def create_shelf_widget(self):
        """创建shelf窗口"""
        try:
            self.shelf_widget = ImprovedShelfWidget()
            
            # 连接信号
            self.shelf_widget.shelf_shown.connect(self.on_shelf_shown)
            self.shelf_widget.shelf_hidden.connect(self.on_shelf_hidden)
            
            # 初始隐藏
            self.shelf_widget.hide()
            
            print("✅ 改进版Shelf窗口创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建shelf窗口时出错: {e}")
            return False
    
    def setup_gesture_detector(self):
        """设置手势检测"""
        try:
            self.gesture_detector = GestureDetector()
            
            # 连接信号
            self.gesture_detector.shake_detected.connect(self.on_shake_detected)
            self.gesture_detector.drag_started.connect(self.on_drag_started)
            self.gesture_detector.drag_ended.connect(self.on_drag_ended)
            
            # 启动检测
            self.gesture_detector.start_detection()
            
            print("✅ 手势检测设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 设置手势检测时出错: {e}")
            return False
    
    def on_shake_detected(self, x, y):
        """处理抖动手势检测"""
        # 只有在窗口隐藏时才响应抖动
        if not self.shelf_visible:
            print(f"🎉 检测到抖动手势 at ({x}, {y}) - 显示shelf")
            
            # 计算shelf位置
            shelf_x = x - 100  # 向左偏移
            shelf_y = y + 30   # 向下偏移
            
            # 显示shelf
            self.shelf_widget.show_at_position(shelf_x, shelf_y)
        else:
            print("⚠️ Shelf已显示，忽略抖动手势")
    
    def on_drag_started(self):
        """处理拖拽开始事件"""
        print("📁 开始拖拽文件")
    
    def on_drag_ended(self):
        """处理拖拽结束事件"""
        print("📁 拖拽文件结束")
    
    def on_shelf_shown(self):
        """处理shelf显示事件"""
        self.shelf_visible = True
        print("👁️ Shelf已显示 - 停止手势检测")
    
    def on_shelf_hidden(self):
        """处理shelf隐藏事件"""
        self.shelf_visible = False
        print("🙈 Shelf已隐藏 - 恢复手势检测")
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 启动改进版DropShelf...")
            
            # 1. 设置应用程序
            self.setup_application()
            
            # 2. 创建shelf窗口
            if not self.create_shelf_widget():
                return 1
            
            # 3. 设置手势检测
            gesture_ok = self.setup_gesture_detector()
            
            print("\n" + "=" * 60)
            print("🎉 DropShelf 改进版正在运行！")
            print("=" * 60)
            print("✨ 新功能:")
            print("  📦 统一图标显示所有文件")
            print("  🔄 双击图标展开/收起文件列表")
            print("  🚫 自动去重，相同文件不会重复添加")
            print("  🖱️ 只在拖拽文件时检测抖动手势")
            print("  🎯 窗口显示时停止手势检测，避免位置变化")
            print()
            print("📖 使用方法:")
            if gesture_ok:
                print("  1. 🖱️ 拖拽文件时快速左右抖动鼠标召唤shelf")
                print("  2. 📁 拖拽文件到shelf进行存储")
                print("  3. 🔄 双击图标展开查看所有文件")
                print("  4. 🚀 拖拽图标可一次性拖出所有文件")
                print("  5. 📄 拖拽展开列表中的单个文件")
                print("  6. 🚪 按Esc键手动关闭shelf")
            else:
                print("  ⚠️ 手势检测失败，请手动测试")
            print("=" * 60)
            
            # 启动事件循环
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断应用程序")
            return 0
        except Exception as e:
            print(f"\n❌ 致命错误: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            # 清理资源
            if self.gesture_detector:
                try:
                    self.gesture_detector.stop_detection()
                    print("🧹 手势检测已停止")
                except:
                    pass


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    missing_deps = []
    
    try:
        import PyQt6
        print(f"  ✅ PyQt6: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        missing_deps.append("PyQt6")
        print("  ❌ PyQt6: 未安装")
    
    try:
        import pynput
        print("  ✅ pynput: 可用")
    except ImportError:
        missing_deps.append("pynput")
        print("  ❌ pynput: 未安装")
    
    try:
        from PIL import Image
        print("  ✅ Pillow: 可用")
    except ImportError:
        missing_deps.append("Pillow")
        print("  ⚠️ Pillow: 未安装 (可选)")
    
    try:
        import win32gui
        print("  ✅ pywin32: 可用")
    except ImportError:
        missing_deps.append("pywin32")
        print("  ❌ pywin32: 未安装")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖项: {', '.join(missing_deps)}")
        print("请安装缺少的依赖项:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    print("✅ 所有依赖项都已安装")
    return True


def main():
    """主函数"""
    print("=" * 70)
    print("DropShelf - 改进版 Windows 生产力工具")
    print("=" * 70)
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 创建并运行应用程序
    app = ImprovedDropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
