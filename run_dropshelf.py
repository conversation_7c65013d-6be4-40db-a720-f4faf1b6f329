#!/usr/bin/env python3
"""
Simple launcher script for DropShelf
This script provides an easy way to run DropShelf with different configurations
"""

import sys
import os
import argparse


def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description="DropShelf Launcher")
    parser.add_argument("--debug", action="store_true", 
                       help="Enable debug mode with extended auto-hide timer")
    parser.add_argument("--no-auto-hide", action="store_true",
                       help="Disable auto-hide functionality")
    
    args = parser.parse_args()
    
    # Set environment variables based on arguments
    if args.debug:
        os.environ["DROPSHELF_DEBUG"] = "1"
        print("Debug mode enabled - Extended auto-hide timer")
    
    if args.no_auto_hide:
        os.environ["DROPSHELF_NO_AUTO_HIDE"] = "1"
        print("Auto-hide disabled")
    
    # Import and run the main application
    try:
        from main import main as dropshelf_main
        return dropshelf_main()
    except ImportError as e:
        print(f"Error importing DropShelf: {e}")
        print("Make sure main.py is in the same directory")
        return 1
    except Exception as e:
        print(f"Error running DropShelf: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
