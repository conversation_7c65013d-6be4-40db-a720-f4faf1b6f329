#!/usr/bin/env python3
"""
DropShelf - Windows Productivity Tool
Main application entry point

A floating shelf that can be summoned by shaking the mouse while dragging files.
Provides temporary storage for files that can be dragged to other locations.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon

# Import our custom components
from shelf_widget import ShelfWidget
from gesture_detector import GestureDetector


class DropShelfApp:
    """Main application class for DropShelf"""
    
    def __init__(self):
        """Initialize the DropShelf application"""
        self.app = None
        self.shelf_widget = None
        self.test_timer = None
        self.gesture_detector = None
        
    def setup_application(self):
        """Setup the QApplication with proper configuration"""
        # Create QApplication instance
        self.app = QApplication(sys.argv)
        
        # Set application properties
        self.app.setApplicationName("DropShelf")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("DropShelf")
        
        # Enable high DPI scaling for modern displays
        # Note: AA_EnableHighDpiScaling is deprecated in Qt6, high DPI is enabled by default
        try:
            self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # Attribute may not exist in newer PyQt6 versions
            pass

        # Set application icon if available
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "assets", "icon.ico")
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
        except (FileNotFoundError, OSError) as e:
            print(f"Warning: Could not load application icon: {e}")

    def create_shelf_widget(self):
        """Create and configure the main shelf widget"""
        try:
            self.shelf_widget = ShelfWidget()

            # Hide the shelf initially - it will be shown by gesture detection
            self.shelf_widget.hide()

            print("DropShelf initialized successfully!")
            print("Shelf widget created (hidden, waiting for shake gesture)")

        except ImportError as e:
            print(f"Error importing shelf widget: {e}")
            sys.exit(1)
        except RuntimeError as e:
            print(f"Error creating shelf widget: {e}")
            sys.exit(1)

    def setup_gesture_detector(self):
        """Setup and start the gesture detection system"""
        try:
            self.gesture_detector = GestureDetector()

            # Connect signals
            self.gesture_detector.shake_detected.connect(self.on_shake_detected)
            self.gesture_detector.drag_started.connect(self.on_drag_started)
            self.gesture_detector.drag_ended.connect(self.on_drag_ended)

            # Start detection
            self.gesture_detector.start_detection()

            print("Gesture detection started - shake mouse while dragging to summon shelf")

        except Exception as e:
            print(f"Error setting up gesture detector: {e}")
            print("Continuing without gesture detection...")

    def on_shake_detected(self, x, y):
        """Handle shake gesture detection"""
        print(f"Shake detected at ({x}, {y}) - showing shelf")

        # Position shelf near cursor but not directly under it
        shelf_x = x - 200  # Offset to the left
        shelf_y = y + 50   # Offset below cursor

        # Ensure shelf stays on screen
        screen = self.app.primaryScreen().geometry()
        shelf_x = max(0, min(shelf_x, screen.width() - 400))
        shelf_y = max(0, min(shelf_y, screen.height() - 80))

        # Show shelf at calculated position
        self.shelf_widget.show_shelf((shelf_x, shelf_y))

    def on_drag_started(self):
        """Handle drag start event"""
        print("Drag operation started")

    def on_drag_ended(self):
        """Handle drag end event"""
        print("Drag operation ended")
    
    def setup_testing_timer(self):
        """Setup a timer for testing auto-hide functionality"""
        # Create a timer to test auto-hide after 10 seconds
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.test_auto_hide)
        self.test_timer.setSingleShot(True)
        self.test_timer.start(10000)  # 10 seconds
    
    def test_auto_hide(self):
        """Test function to demonstrate auto-hide functionality"""
        if self.shelf_widget and self.shelf_widget.isVisible():
            print("Testing auto-hide functionality...")
            self.shelf_widget.hide_shelf()
    
    def run(self):
        """Run the main application"""
        try:
            # Setup application
            self.setup_application()

            # Create shelf widget
            self.create_shelf_widget()

            # Setup gesture detection
            self.setup_gesture_detector()

            # Setup testing timer (optional)
            # self.setup_testing_timer()

            # Start the application event loop
            return self.app.exec()

        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            # Clean up gesture detector
            if self.gesture_detector:
                self.gesture_detector.stop_detection()
            return 0
        except Exception as e:
            print(f"Fatal error: {e}")
            # Clean up gesture detector
            if self.gesture_detector:
                self.gesture_detector.stop_detection()
            return 1


def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import PyQt6
        print(f"✓ PyQt6 version: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import pynput
        print(f"✓ pynput available")
    except ImportError:
        missing_deps.append("pynput")
    
    try:
        from PIL import Image
        print(f"✓ Pillow available")
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    print("✓ All dependencies available")
    return True


def main():
    """Main entry point"""
    print("=" * 50)
    print("DropShelf - Windows Productivity Tool")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Create and run application
    app = DropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
