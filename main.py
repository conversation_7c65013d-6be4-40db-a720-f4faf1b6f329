#!/usr/bin/env python3
"""
DropShelf - Windows Productivity Tool
Main application entry point

A floating shelf that can be summoned by shaking the mouse while dragging files.
Provides temporary storage for files that can be dragged to other locations.
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon

# Import our custom shelf widget
from shelf_widget import ShelfWidget


class DropShelfApp:
    """Main application class for DropShelf"""
    
    def __init__(self):
        """Initialize the DropShelf application"""
        self.app = None
        self.shelf_widget = None
        self.test_timer = None
        
    def setup_application(self):
        """Setup the QApplication with proper configuration"""
        # Create QApplication instance
        self.app = QApplication(sys.argv)
        
        # Set application properties
        self.app.setApplicationName("DropShelf")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("DropShelf")
        
        # Enable high DPI scaling for modern displays
        # Note: AA_EnableHighDpiScaling is deprecated in Qt6, high DPI is enabled by default
        try:
            self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # Attribute may not exist in newer PyQt6 versions
            pass

        # Set application icon if available
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "assets", "icon.ico")
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
        except (FileNotFoundError, OSError) as e:
            print(f"Warning: Could not load application icon: {e}")

    def create_shelf_widget(self):
        """Create and configure the main shelf widget"""
        try:
            self.shelf_widget = ShelfWidget()

            # For testing purposes, show the shelf initially
            # In final implementation, this will be hidden by default
            self.shelf_widget.show()

            # Position the shelf in the center of the screen for testing
            screen = self.app.primaryScreen().geometry()
            shelf_width = 400  # Initial width
            shelf_height = 80  # Initial height

            x = (screen.width() - shelf_width) // 2
            y = (screen.height() - shelf_height) // 2

            self.shelf_widget.setGeometry(x, y, shelf_width, shelf_height)

            print("DropShelf initialized successfully!")
            print("Shelf widget created and positioned at center of screen")

        except ImportError as e:
            print(f"Error importing shelf widget: {e}")
            sys.exit(1)
        except RuntimeError as e:
            print(f"Error creating shelf widget: {e}")
            sys.exit(1)
    
    def setup_testing_timer(self):
        """Setup a timer for testing auto-hide functionality"""
        # Create a timer to test auto-hide after 10 seconds
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.test_auto_hide)
        self.test_timer.setSingleShot(True)
        self.test_timer.start(10000)  # 10 seconds
    
    def test_auto_hide(self):
        """Test function to demonstrate auto-hide functionality"""
        if self.shelf_widget and self.shelf_widget.isVisible():
            print("Testing auto-hide functionality...")
            self.shelf_widget.hide_shelf()
    
    def run(self):
        """Run the main application"""
        try:
            # Setup application
            self.setup_application()
            
            # Create shelf widget
            self.create_shelf_widget()
            
            # Setup testing timer
            self.setup_testing_timer()
            
            # Start the application event loop
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            return 0
        except Exception as e:
            print(f"Fatal error: {e}")
            return 1


def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import PyQt6
        print(f"✓ PyQt6 version: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import pynput
        print(f"✓ pynput available")
    except ImportError:
        missing_deps.append("pynput")
    
    try:
        from PIL import Image
        print(f"✓ Pillow available")
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    print("✓ All dependencies available")
    return True


def main():
    """Main entry point"""
    print("=" * 50)
    print("DropShelf - Windows Productivity Tool")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Create and run application
    app = DropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
