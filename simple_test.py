#!/usr/bin/env python3
"""
简化测试版本 - 直接显示shelf窗口进行测试
"""

import sys
from PyQt6.QtWidgets import QApplication, QWidget, QLabel, QHBoxLayout
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QColor, QPainter, QBrush, QLinearGradient


class SimpleShelf(QWidget):
    """简化的shelf窗口用于测试"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """设置窗口属性"""
        # 设置窗口标志
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        
        # 设置半透明背景
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        # 设置窗口大小和位置
        self.setFixedSize(400, 80)
        
        # 居中显示
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - 400) // 2
        y = (screen.height() - 80) // 2
        self.move(x, y)
        
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        label = QLabel("🎉 DropShelf 测试窗口 - 拖拽文件到这里!")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        layout.addWidget(label)
        
        # 设置接受拖放
        self.setAcceptDrops(True)
        
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(40, 40, 40, 220))
        gradient.setColorAt(1, QColor(20, 20, 20, 240))
        
        # 绘制圆角矩形
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), 12, 12)
        
    def dragEnterEvent(self, event):
        """处理拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            print("文件拖拽进入窗口!")
        
    def dropEvent(self, event):
        """处理文件放置事件"""
        if event.mimeData().hasUrls():
            files = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    files.append(url.toLocalFile())
            
            print(f"文件放置成功: {files}")
            event.acceptProposedAction()
            
            # 更新标签显示
            if hasattr(self, 'layout') and self.layout().count() > 0:
                label = self.layout().itemAt(0).widget()
                label.setText(f"✅ 接收到 {len(files)} 个文件!")
    
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("按下Esc键，关闭窗口")
            self.close()
        super().keyPressEvent(event)


def main():
    """主函数"""
    print("=" * 50)
    print("DropShelf 简化测试")
    print("=" * 50)
    print("1. 窗口将显示在屏幕中央")
    print("2. 尝试拖拽文件到窗口上")
    print("3. 按Esc键关闭窗口")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建并显示窗口
    shelf = SimpleShelf()
    shelf.show()
    
    print("✅ 窗口已显示!")
    print("如果看不到窗口，可能是:")
    print("1. 窗口被其他程序遮挡")
    print("2. 显示器分辨率问题")
    print("3. PyQt6兼容性问题")
    
    # 5秒后自动关闭（用于测试）
    timer = QTimer()
    timer.timeout.connect(lambda: (print("5秒测试完成，自动关闭"), app.quit()))
    timer.setSingleShot(True)
    timer.start(5000)
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
