"""
DropShelf Shelf Widget
Core floating shelf window implementation using PyQt6
"""

from PyQt6.QtWidgets import (QWidget, QHBoxLayout, QLabel, QVBoxLayout,
                             QGraphicsDropShadowEffect, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QPoint
from PyQt6.QtGui import QPalette, QColor, QPainter, QBrush, QLinearGradient
from item_widget import DraggableFileItem


class ShelfWidget(QWidget):
    """
    Main floating shelf widget that serves as a temporary file storage area.
    Features:
    - Frameless, always-on-top window
    - Semi-transparent background with modern styling
    - Drag-and-drop support for files
    - Auto-hide functionality
    - Dynamic sizing based on content
    """
    
    # Signals
    shelf_hidden = pyqtSignal()
    shelf_shown = pyqtSignal()
    files_dropped = pyqtSignal(list)  # Emitted when files are dropped
    
    def __init__(self, parent=None):
        """Initialize the shelf widget"""
        super().__init__(parent)
        
        # Configuration constants
        self.MIN_WIDTH = 200
        self.MIN_HEIGHT = 80
        self.MAX_WIDTH = 1200
        self.PADDING = 10
        self.BORDER_RADIUS = 12
        
        # State variables
        self.is_auto_hide_enabled = True
        self.hide_timer = None
        self.fade_animation = None
        
        # Setup the widget
        self.setup_window_properties()
        self.setup_layout()
        self.setup_styling()
        self.setup_auto_hide()
        self.setup_drag_drop()
        
        print("ShelfWidget initialized successfully")
    
    def setup_window_properties(self):
        """Configure window flags and properties"""
        # Set window flags for frameless, always-on-top behavior
        window_flags = (
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool |  # Prevents taskbar entry
            Qt.WindowType.WindowDoesNotAcceptFocus  # Prevents stealing focus
        )
        self.setWindowFlags(window_flags)
        
        # Enable translucent background
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        
        # Set initial size
        self.resize(self.MIN_WIDTH, self.MIN_HEIGHT)
        
        # Set size policy
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
    
    def setup_layout(self):
        """Setup the main layout for shelf items"""
        # Create main horizontal layout for shelf items
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(self.PADDING, self.PADDING, 
                                          self.PADDING, self.PADDING)
        self.main_layout.setSpacing(8)
        
        # Add a placeholder label for empty shelf
        self.placeholder_label = QLabel("Drop files here")
        self.placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.placeholder_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 180);
                font-size: 14px;
                font-weight: 500;
                padding: 20px;
            }
        """)
        self.main_layout.addWidget(self.placeholder_label)
        
        # List to keep track of shelf items
        self.shelf_items = []
    
    def setup_styling(self):
        """Setup the visual styling of the shelf"""
        # Create drop shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # Set the main stylesheet
        self.setStyleSheet("""
            ShelfWidget {
                background: rgba(30, 30, 30, 200);
                border: 1px solid rgba(255, 255, 255, 50);
                border-radius: 12px;
            }
        """)
    
    def setup_auto_hide(self):
        """Setup auto-hide functionality"""
        import os

        # Create timer for auto-hide
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_shelf)
        self.hide_timer.setSingleShot(True)

        # Check for debug mode or disabled auto-hide
        if os.getenv("DROPSHELF_NO_AUTO_HIDE"):
            self.is_auto_hide_enabled = False
            print("Auto-hide disabled via environment variable")

        # Auto-hide delay - longer in debug mode
        if os.getenv("DROPSHELF_DEBUG"):
            self.auto_hide_delay = 30000  # 30 seconds in debug mode
            print("Debug mode: Extended auto-hide delay (30 seconds)")
        else:
            self.auto_hide_delay = 5000  # 5 seconds normally
    
    def setup_drag_drop(self):
        """Enable drag and drop functionality"""
        self.setAcceptDrops(True)
        print("Drag and drop enabled for shelf widget")
    
    def paintEvent(self, event):
        """Custom paint event for modern background styling"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Create gradient background
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(40, 40, 40, 220))
        gradient.setColorAt(1, QColor(20, 20, 20, 240))
        
        # Draw rounded rectangle background
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), self.BORDER_RADIUS, self.BORDER_RADIUS)
        
        super().paintEvent(event)
    
    def show_shelf(self, position=None):
        """Show the shelf at the specified position"""
        if position:
            self.move(position)
        
        self.show()
        self.raise_()  # Bring to front
        
        # Start auto-hide timer if enabled
        if self.is_auto_hide_enabled:
            self.start_auto_hide_timer()
        
        self.shelf_shown.emit()
        print(f"Shelf shown at position: {self.pos()}")
    
    def hide_shelf(self):
        """Hide the shelf with optional fade animation"""
        self.hide()
        self.shelf_hidden.emit()
        print("Shelf hidden")
    
    def start_auto_hide_timer(self):
        """Start or restart the auto-hide timer"""
        if self.hide_timer:
            self.hide_timer.start(self.auto_hide_delay)
    
    def stop_auto_hide_timer(self):
        """Stop the auto-hide timer"""
        if self.hide_timer:
            self.hide_timer.stop()
    
    def enterEvent(self, event):
        """Handle mouse enter event - stop auto-hide timer"""
        self.stop_auto_hide_timer()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Handle mouse leave event - restart auto-hide timer"""
        if self.is_auto_hide_enabled:
            self.start_auto_hide_timer()
        super().leaveEvent(event)
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key.Key_Escape:
            self.hide_shelf()
        super().keyPressEvent(event)
    
    def dragEnterEvent(self, event):
        """Handle drag enter events for file drops"""
        # Check if the dragged data contains file URLs
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            print("Drag enter: Files detected")
        else:
            event.ignore()
    
    def dragMoveEvent(self, event):
        """Handle drag move events"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event):
        """Handle file drop events"""
        if event.mimeData().hasUrls():
            # Extract file paths from URLs
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_paths.append(url.toLocalFile())
            
            if file_paths:
                print(f"Files dropped: {file_paths}")
                self.files_dropped.emit(file_paths)
                self.add_files_to_shelf(file_paths)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def add_files_to_shelf(self, file_paths):
        """Add dropped files to the shelf using draggable file items"""
        # Hide placeholder if this is the first item
        if not self.shelf_items and self.placeholder_label:
            self.placeholder_label.hide()

        # Create draggable file items for each file
        for file_path in file_paths:
            # Create a draggable file item
            file_item = DraggableFileItem(file_path, self)

            # Connect signals
            file_item.item_removed.connect(self.remove_item_from_shelf)
            file_item.item_selected.connect(self.on_item_selection_changed)

            # Add to layout and track
            self.main_layout.addWidget(file_item)
            self.shelf_items.append(file_item)

        # Adjust window size based on content
        self.adjust_size()
        print(f"Added {len(file_paths)} draggable items to shelf")

    def remove_item_from_shelf(self, item):
        """Remove an item from the shelf"""
        if item in self.shelf_items:
            self.shelf_items.remove(item)
            self.main_layout.removeWidget(item)
            item.deleteLater()

            # Show placeholder if shelf is empty
            if not self.shelf_items and self.placeholder_label:
                self.placeholder_label.show()

            # Adjust size
            self.adjust_size()
            print(f"Removed item from shelf. Remaining items: {len(self.shelf_items)}")

    def on_item_selection_changed(self, item, selected):
        """Handle item selection changes"""
        # For single selection mode, deselect other items
        if selected:
            for shelf_item in self.shelf_items:
                if shelf_item != item and hasattr(shelf_item, 'is_selected') and shelf_item.is_selected:
                    shelf_item.set_selected(False)

        print(f"Item selection changed: {item.filename} -> {selected}")

    def clear_selection(self):
        """Clear all item selections"""
        for item in self.shelf_items:
            if hasattr(item, 'set_selected'):
                item.set_selected(False)
    
    def adjust_size(self):
        """Dynamically adjust the shelf size based on content"""
        # Calculate required width based on items
        item_count = len(self.shelf_items)
        if item_count == 0:
            target_width = self.MIN_WIDTH
        else:
            # Estimate width: padding + items + spacing
            estimated_width = (self.PADDING * 2) + (item_count * 120) + ((item_count - 1) * 8)
            target_width = min(max(estimated_width, self.MIN_WIDTH), self.MAX_WIDTH)
        
        self.resize(target_width, self.MIN_HEIGHT)
        print(f"Shelf resized to: {target_width}x{self.MIN_HEIGHT}")
