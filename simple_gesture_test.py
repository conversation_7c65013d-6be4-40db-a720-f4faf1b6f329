#!/usr/bin/env python3
"""
简单的手势检测测试 - 不需要拖拽，只检测鼠标抖动
"""

import sys
import time
import threading
from collections import deque
import math

try:
    from pynput import mouse
    print("✅ pynput imported successfully")
except ImportError:
    print("❌ pynput not available")
    sys.exit(1)


class SimpleGestureDetector:
    """简化的手势检测器"""
    
    def __init__(self):
        self.is_running = False
        self.mouse_positions = deque(maxlen=50)
        self.mouse_listener = None
        
        # 更敏感的设置
        self.SHAKE_THRESHOLD = 30  # 更低的阈值
        self.SHAKE_TIME_WINDOW = 500  # 更长的时间窗口
        self.MIN_DIRECTION_CHANGES = 2  # 更少的方向变化
        
        print(f"🔧 Gesture detector initialized")
        print(f"   Threshold: {self.SHAKE_THRESHOLD} pixels")
        print(f"   Time window: {self.SHAKE_TIME_WINDOW} ms")
        print(f"   Min direction changes: {self.MIN_DIRECTION_CHANGES}")
    
    def start_detection(self):
        """开始检测"""
        if not self.is_running:
            self.is_running = True
            
            # 启动鼠标监听器
            self.mouse_listener = mouse.Listener(
                on_move=self.on_mouse_move,
                on_click=self.on_mouse_click
            )
            self.mouse_listener.start()
            
            # 启动检测线程
            self.detection_thread = threading.Thread(target=self.detection_loop)
            self.detection_thread.daemon = True
            self.detection_thread.start()
            
            print("✅ Gesture detection started")
            print("🖱️ Move your mouse quickly left-right-left to test!")
    
    def stop_detection(self):
        """停止检测"""
        self.is_running = False
        if self.mouse_listener:
            self.mouse_listener.stop()
        print("🛑 Gesture detection stopped")
    
    def on_mouse_move(self, x, y):
        """鼠标移动事件"""
        if self.is_running:
            current_time = time.time() * 1000
            self.mouse_positions.append((x, y, current_time))
    
    def on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if button == mouse.Button.left:
            if pressed:
                print("🖱️ Left button pressed")
            else:
                print("🖱️ Left button released")
    
    def detection_loop(self):
        """检测循环"""
        last_check_time = 0
        
        while self.is_running:
            current_time = time.time()
            
            # 每100ms检查一次
            if current_time - last_check_time > 0.1:
                self.check_for_shake()
                last_check_time = current_time
            
            time.sleep(0.01)  # 10ms
    
    def check_for_shake(self):
        """检查是否有抖动手势"""
        if len(self.mouse_positions) < 6:
            return
        
        # 获取最近的位置
        recent_positions = self.get_recent_positions()
        
        if len(recent_positions) < 4:
            return
        
        # 检查是否是抖动模式
        if self.is_shake_pattern(recent_positions):
            print(f"🎉 SHAKE DETECTED! Positions: {len(recent_positions)}")
            
            # 清空位置历史，避免重复检测
            self.mouse_positions.clear()
    
    def get_recent_positions(self):
        """获取最近时间窗口内的位置"""
        current_time = time.time() * 1000
        cutoff_time = current_time - self.SHAKE_TIME_WINDOW
        
        recent = []
        for pos in reversed(self.mouse_positions):
            if pos[2] >= cutoff_time:
                recent.append(pos)
            else:
                break
        
        return list(reversed(recent))
    
    def is_shake_pattern(self, positions):
        """判断是否是抖动模式"""
        if len(positions) < 4:
            return False
        
        # 计算总移动距离
        total_distance = 0
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            total_distance += math.sqrt(dx*dx + dy*dy)
        
        if total_distance < self.SHAKE_THRESHOLD:
            return False
        
        # 分析水平方向变化
        direction_changes = 0
        last_direction = None
        
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            
            if abs(dx) > 1:  # 忽略微小移动
                current_direction = 1 if dx > 0 else -1
                
                if last_direction is not None and current_direction != last_direction:
                    direction_changes += 1
                
                last_direction = current_direction
        
        # 检查方向变化是否足够
        is_shake = direction_changes >= self.MIN_DIRECTION_CHANGES
        
        if is_shake:
            print(f"   📊 Distance: {total_distance:.1f}, Direction changes: {direction_changes}")
        
        return is_shake


def main():
    """主函数"""
    print("=" * 60)
    print("简单手势检测测试")
    print("=" * 60)
    print("这个测试将检测鼠标的左右抖动手势")
    print("不需要拖拽文件，只需要快速左右移动鼠标")
    print("按 Ctrl+C 退出")
    print("=" * 60)
    
    detector = SimpleGestureDetector()
    
    try:
        detector.start_detection()
        
        # 保持程序运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        detector.stop_detection()
        return 0
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        detector.stop_detection()
        return 1


if __name__ == "__main__":
    sys.exit(main())
