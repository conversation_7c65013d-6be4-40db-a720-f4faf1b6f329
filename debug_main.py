#!/usr/bin/env python3
"""
调试版本的DropShelf - 分步测试各个功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTimer

# Import our custom components
from shelf_widget import ShelfWidget


class DebugDropShelfApp:
    """调试版本的DropShelf应用"""
    
    def __init__(self):
        """Initialize the DropShelf application"""
        self.app = None
        self.shelf_widget = None
        self.test_timer = None
        
    def setup_application(self):
        """Setup the QApplication with proper configuration"""
        # Create QApplication instance
        self.app = QApplication(sys.argv)
        
        # Set application properties
        self.app.setApplicationName("DropShelf Debug")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("DropShelf")
        
        print("✅ QApplication created successfully")
        
    def create_shelf_widget(self):
        """Create and configure the main shelf widget"""
        try:
            print("🔧 Creating shelf widget...")
            self.shelf_widget = ShelfWidget()
            
            print("✅ Shelf widget created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error creating shelf widget: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_show_shelf(self):
        """测试显示shelf窗口"""
        try:
            print("🔧 Testing shelf display...")
            
            # 获取屏幕中心位置
            screen = self.app.primaryScreen().geometry()
            x = (screen.width() - 400) // 2
            y = (screen.height() - 80) // 2
            
            print(f"📍 Screen size: {screen.width()}x{screen.height()}")
            print(f"📍 Positioning shelf at: ({x}, {y})")
            
            # 显示窗口
            self.shelf_widget.move(x, y)
            self.shelf_widget.show()
            self.shelf_widget.raise_()
            self.shelf_widget.activateWindow()
            
            print("✅ Shelf window displayed!")
            print("👀 请检查屏幕中央是否有半透明的黑色窗口")
            
            return True
            
        except Exception as e:
            print(f"❌ Error showing shelf: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def setup_test_timer(self):
        """设置测试定时器"""
        print("⏰ Setting up test timer (10 seconds)...")
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.on_test_complete)
        self.test_timer.setSingleShot(True)
        self.test_timer.start(10000)  # 10 seconds
    
    def on_test_complete(self):
        """测试完成"""
        print("⏰ Test timer completed - closing application")
        if self.shelf_widget and self.shelf_widget.isVisible():
            print("✅ Shelf was visible during test")
        else:
            print("❌ Shelf was not visible during test")
        
        self.app.quit()
    
    def run_basic_test(self):
        """运行基本测试"""
        print("🚀 Starting basic shelf display test...")
        
        try:
            # 1. Setup application
            print("\n1️⃣ Setting up application...")
            self.setup_application()
            
            # 2. Create shelf widget
            print("\n2️⃣ Creating shelf widget...")
            if not self.create_shelf_widget():
                return 1
            
            # 3. Test showing shelf
            print("\n3️⃣ Testing shelf display...")
            if not self.test_show_shelf():
                return 1
            
            # 4. Setup test timer
            print("\n4️⃣ Setting up test timer...")
            self.setup_test_timer()
            
            # 5. Start event loop
            print("\n5️⃣ Starting application event loop...")
            print("=" * 50)
            print("应用程序正在运行...")
            print("请查看屏幕中央是否有DropShelf窗口")
            print("窗口应该是半透明的黑色，显示'Drop files here'")
            print("10秒后自动关闭")
            print("=" * 50)
            
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\n⚠️ Application interrupted by user")
            return 0
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
            import traceback
            traceback.print_exc()
            return 1


def main():
    """主函数"""
    print("=" * 60)
    print("DropShelf 调试测试 - 基本窗口显示")
    print("=" * 60)
    print("这个测试将:")
    print("1. 创建PyQt6应用程序")
    print("2. 创建DropShelf窗口")
    print("3. 在屏幕中央显示窗口")
    print("4. 10秒后自动关闭")
    print("=" * 60)
    
    # 检查依赖
    try:
        import PyQt6
        print(f"✅ PyQt6 available")
    except ImportError:
        print("❌ PyQt6 not available")
        return 1
    
    # 运行测试
    app = DebugDropShelfApp()
    return app.run_basic_test()


if __name__ == "__main__":
    sys.exit(main())
