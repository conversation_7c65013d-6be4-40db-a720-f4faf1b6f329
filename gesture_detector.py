"""
DropShelf Gesture Detector
Mouse shake detection for summoning the shelf while dragging files
"""

import threading
import time
import math
from collections import deque
from PyQt6.QtCore import QObject, pyqtSignal
from pynput import mouse
import win32gui
import win32con


class GestureDetector(QObject, threading.Thread):
    """
    Detects mouse shake gestures while dragging files.
    Runs in a separate thread to monitor global mouse movements.
    """
    
    # Signals
    shake_detected = pyqtSignal(int, int)  # x, y position where shake was detected
    drag_started = pyqtSignal()
    drag_ended = pyqtSignal()
    
    def __init__(self):
        """Initialize the gesture detector"""
        QObject.__init__(self)
        threading.Thread.__init__(self)

        # Configuration - More sensitive settings for easier triggering
        self.SHAKE_THRESHOLD = 40  # Minimum distance for shake detection
        self.SHAKE_TIME_WINDOW = 400  # Time window in milliseconds
        self.MIN_DIRECTION_CHANGES = 2  # Minimum direction changes for shake
        self.SAMPLE_INTERVAL = 0.01  # 10ms sampling interval

        # State variables
        self.is_running = False
        self.is_mouse_pressed = False  # 改为检测鼠标按下状态
        self.mouse_positions = deque(maxlen=50)  # Store recent positions
        self.last_shake_time = 0
        self.shake_cooldown = 1.0  # 1 second cooldown between shakes

        # Mouse listener
        self.mouse_listener = None

        print("GestureDetector initialized - 检测鼠标按下时的抖动")
    
    def start_detection(self):
        """Start the gesture detection thread"""
        if not self.is_running:
            self.is_running = True
            self.start()  # Start the thread
            print("Gesture detection started")
    
    def stop_detection(self):
        """Stop the gesture detection"""
        self.is_running = False
        if self.mouse_listener:
            self.mouse_listener.stop()
        print("Gesture detection stopped")
    
    def run(self):
        """Main thread loop for gesture detection"""
        try:
            # Start mouse listener
            self.mouse_listener = mouse.Listener(
                on_move=self.on_mouse_move,
                on_click=self.on_mouse_click
            )
            self.mouse_listener.start()
            
            # Main detection loop
            while self.is_running:
                # 只在鼠标按下时检查抖动手势
                if self.is_mouse_pressed:
                    self.check_for_shake()
                time.sleep(self.SAMPLE_INTERVAL)
                
        except Exception as e:
            print(f"Error in gesture detection thread: {e}")
        finally:
            if self.mouse_listener:
                self.mouse_listener.stop()
    
    def on_mouse_move(self, x, y):
        """Handle mouse movement events"""
        if self.is_running:
            current_time = time.time() * 1000  # Convert to milliseconds
            self.mouse_positions.append((x, y, current_time))
    
    def on_mouse_click(self, x, y, button, pressed):
        """Handle mouse click events"""
        if button == mouse.Button.left:
            if pressed:
                # Left button pressed - start monitoring for shake
                self.is_mouse_pressed = True
                self.mouse_positions.clear()
                print("🖱️ 鼠标按下 - 开始监控抖动")
            else:
                # Left button released - stop monitoring
                self.is_mouse_pressed = False
                print("🖱️ 鼠标松开 - 停止监控抖动")
    

    
    def check_for_shake(self):
        """Analyze recent mouse movements for shake pattern"""
        if len(self.mouse_positions) < 10:
            return
        
        current_time = time.time()
        
        # Check cooldown
        if current_time - self.last_shake_time < self.shake_cooldown:
            return
        
        # Get recent positions within time window
        recent_positions = self.get_recent_positions()
        
        if len(recent_positions) < 6:
            return
        
        # Analyze for shake pattern
        if self.is_shake_pattern(recent_positions):
            # Get current cursor position for shelf placement
            try:
                cursor_pos = win32gui.GetCursorPos()
                self.last_shake_time = current_time
                self.shake_detected.emit(cursor_pos[0], cursor_pos[1])
                print(f"Shake detected at position: {cursor_pos}")
            except Exception as e:
                print(f"Error getting cursor position: {e}")
    
    def get_recent_positions(self):
        """Get mouse positions within the time window"""
        current_time = time.time() * 1000
        cutoff_time = current_time - self.SHAKE_TIME_WINDOW
        
        recent = []
        for pos in reversed(self.mouse_positions):
            if pos[2] >= cutoff_time:
                recent.append(pos)
            else:
                break
        
        return list(reversed(recent))
    
    def is_shake_pattern(self, positions):
        """Determine if the position sequence represents a shake"""
        if len(positions) < 6:
            return False
        
        # Calculate total movement distance
        total_distance = 0
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            total_distance += math.sqrt(dx*dx + dy*dy)
        
        # Check if total movement exceeds threshold
        if total_distance < self.SHAKE_THRESHOLD:
            return False
        
        # Analyze direction changes (horizontal shake pattern)
        direction_changes = 0
        last_direction = None
        
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            
            if abs(dx) > 2:  # Ignore tiny movements
                current_direction = 1 if dx > 0 else -1
                
                if last_direction is not None and current_direction != last_direction:
                    direction_changes += 1
                
                last_direction = current_direction
        
        # Check for sufficient direction changes (left-right-left pattern)
        return direction_changes >= self.MIN_DIRECTION_CHANGES
    
    def set_sensitivity(self, threshold=None, time_window=None, min_changes=None):
        """Adjust shake detection sensitivity"""
        if threshold is not None:
            self.SHAKE_THRESHOLD = threshold
            print(f"Shake threshold set to: {threshold}")
        
        if time_window is not None:
            self.SHAKE_TIME_WINDOW = time_window
            print(f"Time window set to: {time_window}ms")
        
        if min_changes is not None:
            self.MIN_DIRECTION_CHANGES = min_changes
            print(f"Minimum direction changes set to: {min_changes}")
