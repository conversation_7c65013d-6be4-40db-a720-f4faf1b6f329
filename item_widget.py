"""
DropShelf Item Widget
Custom widget for individual shelf items that can be dragged out
"""

import os
from PyQt6.QtWidgets import QLabel, QWidget, QVBoxLayout, QHBoxLayout
from PyQt6.QtCore import Qt, QMimeData, QUrl, pyqtSignal, QPoint
from PyQt6.QtGui import QDrag, QPainter, QPixmap, QIcon, QFont, QColor, QPen, QBrush


class DraggableFileItem(QWidget):
    """
    A draggable widget representing a file on the shelf.
    Supports both individual and multi-file drag operations.
    """
    
    # Signals
    item_removed = pyqtSignal(object)  # Emitted when item is dragged out
    item_selected = pyqtSignal(object, bool)  # Emitted when selection changes
    
    def __init__(self, file_path, parent=None):
        """Initialize the draggable file item"""
        super().__init__(parent)
        
        self.file_path = file_path
        self.filename = os.path.basename(file_path)
        self.is_selected = False
        self.drag_start_position = QPoint()
        
        # Setup the widget
        self.setup_ui()
        self.setup_styling()
        
        # Enable mouse tracking for hover effects
        self.setMouseTracking(True)
        
        print(f"Created draggable item for: {self.filename}")
    
    def setup_ui(self):
        """Setup the user interface"""
        self.setFixedSize(120, 60)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(2)
        
        # File icon (placeholder for now)
        self.icon_label = QLabel("📄")
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet("font-size: 20px;")
        
        # File name label
        self.name_label = QLabel(self.filename)
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setWordWrap(True)
        
        # Set font for filename
        font = QFont()
        font.setPointSize(9)
        self.name_label.setFont(font)
        
        layout.addWidget(self.icon_label)
        layout.addWidget(self.name_label)
        
        # Set tooltip
        self.setToolTip(self.file_path)
    
    def setup_styling(self):
        """Setup the visual styling"""
        self.update_style()
    
    def update_style(self):
        """Update the widget style based on state"""
        if self.is_selected:
            background_color = "rgba(100, 150, 255, 180)"
            border_color = "rgba(100, 150, 255, 255)"
            text_color = "white"
        else:
            background_color = "rgba(60, 60, 60, 180)"
            border_color = "rgba(80, 80, 80, 200)"
            text_color = "rgba(255, 255, 255, 220)"
        
        self.setStyleSheet(f"""
            DraggableFileItem {{
                background: {background_color};
                border: 1px solid {border_color};
                border-radius: 6px;
            }}
            DraggableFileItem:hover {{
                background: rgba(80, 80, 80, 200);
                border: 1px solid rgba(120, 120, 120, 255);
            }}
        """)
        
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                background: transparent;
                border: none;
            }}
        """)
    
    def set_selected(self, selected):
        """Set the selection state"""
        if self.is_selected != selected:
            self.is_selected = selected
            self.update_style()
            self.item_selected.emit(self, selected)
    
    def mousePressEvent(self, event):
        """Handle mouse press events"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.pos()
            
            # Handle selection
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                # Ctrl+click for multi-selection
                self.set_selected(not self.is_selected)
            else:
                # Regular click - select this item only
                self.set_selected(True)
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """Handle mouse move events for drag initiation"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return
        
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
        
        # Start drag operation
        self.start_drag()
    
    def start_drag(self):
        """Initiate drag operation"""
        # Get all selected items from parent shelf
        selected_items = self.get_selected_items()
        
        if not selected_items:
            selected_items = [self]
        
        # Create drag object
        drag = QDrag(self)
        mime_data = QMimeData()
        
        # Add file URLs to mime data
        urls = []
        for item in selected_items:
            urls.append(QUrl.fromLocalFile(item.file_path))
        
        mime_data.setUrls(urls)
        drag.setMimeData(mime_data)
        
        # Create drag pixmap
        pixmap = self.create_drag_pixmap(selected_items)
        drag.setPixmap(pixmap)
        drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
        
        print(f"Starting drag operation with {len(selected_items)} file(s)")
        
        # Execute drag
        drop_action = drag.exec(Qt.DropAction.CopyAction | Qt.DropAction.MoveAction)
        
        # Handle drag completion
        if drop_action == Qt.DropAction.MoveAction:
            # Remove items from shelf if moved
            for item in selected_items:
                item.item_removed.emit(item)
        
        print(f"Drag completed with action: {drop_action}")
    
    def get_selected_items(self):
        """Get all selected items from the parent shelf"""
        selected_items = []
        
        # Find parent shelf widget
        parent = self.parent()
        while parent and not hasattr(parent, 'shelf_items'):
            parent = parent.parent()
        
        if parent and hasattr(parent, 'shelf_items'):
            for item in parent.shelf_items:
                if hasattr(item, 'is_selected') and item.is_selected:
                    selected_items.append(item)
        
        return selected_items
    
    def create_drag_pixmap(self, items):
        """Create a pixmap for the drag operation"""
        if len(items) == 1:
            # Single item - use the item's appearance
            pixmap = QPixmap(self.size())
            pixmap.fill(Qt.GlobalColor.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Draw background
            painter.setBrush(QBrush(QColor(60, 60, 60, 200)))
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawRoundedRect(pixmap.rect(), 6, 6)
            
            # Draw icon and text
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "📄\n" + self.filename[:8])
            
            painter.end()
            
        else:
            # Multiple items - create a stack representation
            pixmap = QPixmap(140, 80)
            pixmap.fill(Qt.GlobalColor.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Draw multiple rectangles to show stack
            for i in range(min(3, len(items))):
                offset = i * 3
                rect = pixmap.rect().adjusted(offset, offset, -offset, -offset)
                
                alpha = 200 - (i * 50)
                painter.setBrush(QBrush(QColor(60, 60, 60, alpha)))
                painter.setPen(QPen(QColor(100, 100, 100), 1))
                painter.drawRoundedRect(rect, 6, 6)
            
            # Draw count
            painter.setPen(QPen(QColor(255, 255, 255)))
            font = QFont()
            font.setPointSize(12)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, f"{len(items)} files")
            
            painter.end()
        
        return pixmap
    
    def get_file_icon(self):
        """Get appropriate icon for file type (placeholder implementation)"""
        ext = os.path.splitext(self.filename)[1].lower()
        
        icon_map = {
            '.txt': '📄',
            '.doc': '📄', '.docx': '📄',
            '.pdf': '📕',
            '.jpg': '🖼️', '.jpeg': '🖼️', '.png': '🖼️', '.gif': '🖼️',
            '.mp3': '🎵', '.wav': '🎵', '.mp4': '🎬',
            '.zip': '📦', '.rar': '📦',
            '.exe': '⚙️',
            '.py': '🐍',
            '.js': '📜', '.html': '🌐', '.css': '🎨'
        }
        
        return icon_map.get(ext, '📄')


# Import QApplication for drag distance
from PyQt6.QtWidgets import QApplication
