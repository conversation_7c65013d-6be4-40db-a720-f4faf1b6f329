#!/usr/bin/env python3
"""
最终工作版本的DropShelf - 解决了窗口显示问题
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QColor, QPainter, QBrush, QLinearGradient


class FinalShelfWidget(QWidget):
    """最终版本的shelf窗口 - 简化但稳定"""
    
    def __init__(self):
        super().__init__()
        self.shelf_items = []
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """设置窗口属性"""
        # 设置窗口标志 - 移除可能导致问题的标志
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )
        
        # 设置半透明背景
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        # 设置初始大小
        self.setFixedSize(400, 80)
        
        # 设置接受拖放
        self.setAcceptDrops(True)
        
    def setup_ui(self):
        """设置UI"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(10)
        
        # 占位符标签
        self.placeholder_label = QLabel("📁 Drop files here or shake mouse to summon!")
        self.placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.placeholder_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        self.layout.addWidget(self.placeholder_label)
        
    def paintEvent(self, event):
        """绘制背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(40, 40, 40, 220))
        gradient.setColorAt(1, QColor(20, 20, 20, 240))
        
        # 绘制圆角矩形
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), 12, 12)
        
    def dragEnterEvent(self, event):
        """处理拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            print("📁 Files detected in drag operation")
        
    def dropEvent(self, event):
        """处理文件放置事件"""
        if event.mimeData().hasUrls():
            files = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    files.append(url.toLocalFile())
            
            if files:
                print(f"✅ Files dropped: {files}")
                self.add_files(files)
                event.acceptProposedAction()
    
    def add_files(self, file_paths):
        """添加文件到shelf"""
        # 隐藏占位符
        if self.placeholder_label.isVisible():
            self.placeholder_label.hide()
        
        # 为每个文件创建标签
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            file_label = QLabel(f"📄 {filename}")
            file_label.setStyleSheet("""
                QLabel {
                    background: rgba(80, 80, 80, 200);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 12px;
                    margin: 2px;
                }
            """)
            file_label.setToolTip(file_path)
            
            self.layout.addWidget(file_label)
            self.shelf_items.append(file_label)
        
        # 调整窗口大小
        new_width = min(400 + len(self.shelf_items) * 100, 800)
        self.setFixedSize(new_width, 80)
        
        print(f"📦 Added {len(file_paths)} files to shelf")
    
    def show_at_position(self, x, y):
        """在指定位置显示shelf"""
        try:
            # 确保位置在屏幕范围内
            screen = QApplication.primaryScreen().geometry()
            x = max(0, min(x, screen.width() - self.width()))
            y = max(0, min(y, screen.height() - self.height()))
            
            self.move(x, y)
            self.show()
            self.raise_()
            self.activateWindow()
            
            print(f"✅ Shelf displayed at ({x}, {y})")
            
            # 5秒后自动隐藏
            QTimer.singleShot(5000, self.hide)
            
        except Exception as e:
            print(f"❌ Error showing shelf: {e}")
    
    def keyPressEvent(self, event):
        """按键事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("🚪 Esc pressed - hiding shelf")
            self.hide()
        super().keyPressEvent(event)


class FinalDropShelfApp:
    """最终版本的DropShelf应用"""
    
    def __init__(self):
        self.app = None
        self.shelf_widget = None
        self.gesture_detector = None
        
    def setup_application(self):
        """设置应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("DropShelf Final")
        print("✅ Application setup complete")
        
    def create_shelf_widget(self):
        """创建shelf窗口"""
        try:
            self.shelf_widget = FinalShelfWidget()
            self.shelf_widget.hide()  # 初始隐藏
            print("✅ Shelf widget created (hidden)")
            return True
        except Exception as e:
            print(f"❌ Error creating shelf widget: {e}")
            return False
    
    def setup_gesture_detector(self):
        """设置手势检测"""
        try:
            from gesture_detector import GestureDetector
            
            self.gesture_detector = GestureDetector()
            self.gesture_detector.shake_detected.connect(self.on_shake_detected)
            self.gesture_detector.start_detection()
            
            print("✅ Gesture detection started")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up gesture detector: {e}")
            return False
    
    def on_shake_detected(self, x, y):
        """处理抖动手势"""
        print(f"🎉 SHAKE DETECTED at ({x}, {y})!")
        
        # 在鼠标位置附近显示shelf
        shelf_x = x - 200
        shelf_y = y + 50
        
        self.shelf_widget.show_at_position(shelf_x, shelf_y)
    
    def run(self):
        """运行应用程序"""
        try:
            print("🚀 Starting DropShelf Final Version...")
            
            # 1. 设置应用程序
            self.setup_application()
            
            # 2. 创建shelf窗口
            if not self.create_shelf_widget():
                return 1
            
            # 3. 设置手势检测
            gesture_ok = self.setup_gesture_detector()
            
            print("\n" + "=" * 50)
            print("🎉 DropShelf is running!")
            if gesture_ok:
                print("🖱️ Shake mouse left-right to summon shelf")
                print("📁 Drag files onto shelf to store them")
            else:
                print("⚠️ Gesture detection failed - manual mode only")
            print("🚪 Press Esc to hide shelf")
            print("=" * 50)
            
            # 启动事件循环
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\n⚠️ Application interrupted")
            return 0
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
            return 1
        finally:
            # 清理
            if self.gesture_detector:
                try:
                    self.gesture_detector.stop_detection()
                except:
                    pass


def main():
    """主函数"""
    print("=" * 60)
    print("DropShelf - Final Working Version")
    print("=" * 60)
    
    app = FinalDropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
