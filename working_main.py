#!/usr/bin/env python3
"""
工作版本的DropShelf - 包含手势检测但有更好的错误处理
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTimer

# Import our custom components
from shelf_widget import ShelfWidget


class WorkingDropShelfApp:
    """工作版本的DropShelf应用"""
    
    def __init__(self):
        """Initialize the DropShelf application"""
        self.app = None
        self.shelf_widget = None
        self.gesture_detector = None
        
    def setup_application(self):
        """Setup the QApplication with proper configuration"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("DropShelf")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("DropShelf")
        print("✅ Application setup complete")
        
    def create_shelf_widget(self):
        """Create and configure the main shelf widget"""
        try:
            self.shelf_widget = ShelfWidget()
            # 初始隐藏shelf
            self.shelf_widget.hide()
            print("✅ Shelf widget created (hidden)")
            return True
        except Exception as e:
            print(f"❌ Error creating shelf widget: {e}")
            return False
    
    def setup_gesture_detector(self):
        """Setup gesture detection with error handling"""
        try:
            print("🔧 Setting up gesture detector...")
            from gesture_detector import GestureDetector
            
            self.gesture_detector = GestureDetector()
            
            # Connect signals
            self.gesture_detector.shake_detected.connect(self.on_shake_detected)
            self.gesture_detector.drag_started.connect(self.on_drag_started)
            self.gesture_detector.drag_ended.connect(self.on_drag_ended)
            
            # Start detection
            self.gesture_detector.start_detection()
            
            print("✅ Gesture detection started successfully")
            print("🖱️ Try dragging a file and shaking mouse left-right!")
            return True
            
        except ImportError as e:
            print(f"❌ Cannot import gesture detector: {e}")
            print("⚠️ Continuing without gesture detection...")
            return False
        except Exception as e:
            print(f"❌ Error setting up gesture detector: {e}")
            print("⚠️ Continuing without gesture detection...")
            return False
    
    def on_shake_detected(self, x, y):
        """Handle shake gesture detection"""
        print(f"🎉 SHAKE DETECTED at ({x}, {y})!")
        
        try:
            # Calculate shelf position
            shelf_x = max(0, min(x - 200, 1920 - 400))  # Assume max screen width
            shelf_y = max(0, min(y + 50, 1080 - 80))    # Assume max screen height
            
            print(f"📍 Showing shelf at ({shelf_x}, {shelf_y})")
            
            # Show shelf
            self.shelf_widget.move(shelf_x, shelf_y)
            self.shelf_widget.show()
            self.shelf_widget.raise_()
            
            print("✅ Shelf should now be visible!")
            
        except Exception as e:
            print(f"❌ Error showing shelf: {e}")
    
    def on_drag_started(self):
        """Handle drag start"""
        print("📁 Drag operation started")
    
    def on_drag_ended(self):
        """Handle drag end"""
        print("📁 Drag operation ended")
    
    def setup_manual_test(self):
        """设置手动测试 - 按空格键显示shelf"""
        print("⌨️ Manual test: Press SPACE to show shelf, ESC to quit")
        
        # 创建一个隐藏的窗口来捕获按键
        self.key_window = QApplication.activeWindow()
        if not self.key_window:
            # 创建一个最小化的窗口来捕获全局按键
            from PyQt6.QtWidgets import QWidget
            self.key_window = QWidget()
            self.key_window.setWindowTitle("DropShelf Control")
            self.key_window.resize(300, 100)
            self.key_window.show()
            
            # 重写按键事件
            def keyPressEvent(event):
                if event.key() == Qt.Key.Key_Space:
                    print("🎯 SPACE pressed - showing shelf manually")
                    self.show_shelf_manually()
                elif event.key() == Qt.Key.Key_Escape:
                    print("🚪 ESC pressed - quitting")
                    self.app.quit()
            
            self.key_window.keyPressEvent = keyPressEvent
            self.key_window.setFocus()
    
    def show_shelf_manually(self):
        """手动显示shelf用于测试"""
        try:
            # 显示在屏幕中央
            screen = self.app.primaryScreen().geometry()
            x = (screen.width() - 400) // 2
            y = (screen.height() - 80) // 2
            
            self.shelf_widget.move(x, y)
            self.shelf_widget.show()
            self.shelf_widget.raise_()
            
            print(f"✅ Shelf displayed manually at ({x}, {y})")
            
        except Exception as e:
            print(f"❌ Error showing shelf manually: {e}")
    
    def run(self):
        """Run the application"""
        try:
            print("🚀 Starting DropShelf...")
            
            # 1. Setup application
            self.setup_application()
            
            # 2. Create shelf widget
            if not self.create_shelf_widget():
                return 1
            
            # 3. Try to setup gesture detection
            gesture_ok = self.setup_gesture_detector()
            
            # 4. If gesture detection failed, setup manual test
            if not gesture_ok:
                self.setup_manual_test()
            
            print("\n" + "=" * 50)
            print("DropShelf is running!")
            if gesture_ok:
                print("🖱️ Drag files and shake mouse to summon shelf")
            else:
                print("⌨️ Press SPACE to show shelf manually")
                print("⌨️ Press ESC to quit")
            print("=" * 50)
            
            # Start event loop
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\n⚠️ Application interrupted")
            return 0
        except Exception as e:
            print(f"\n❌ Fatal error: {e}")
            import traceback
            traceback.print_exc()
            return 1
        finally:
            # Cleanup
            if self.gesture_detector:
                try:
                    self.gesture_detector.stop_detection()
                    print("🧹 Gesture detector stopped")
                except:
                    pass


def main():
    """主函数"""
    print("=" * 60)
    print("DropShelf - Windows Productivity Tool")
    print("=" * 60)
    
    # Check dependencies
    missing_deps = []
    
    try:
        import PyQt6
        print("✅ PyQt6 available")
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import pynput
        print("✅ pynput available")
    except ImportError:
        missing_deps.append("pynput")
        print("⚠️ pynput not available - gesture detection will be disabled")
    
    try:
        from PIL import Image
        print("✅ Pillow available")
    except ImportError:
        missing_deps.append("Pillow")
        print("⚠️ Pillow not available - image thumbnails will be disabled")
    
    if "PyQt6" in missing_deps:
        print("❌ PyQt6 is required but not available")
        return 1
    
    # Run application
    app = WorkingDropShelfApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
