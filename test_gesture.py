#!/usr/bin/env python3
"""
Test script for gesture detection sensitivity
This script allows you to test and adjust the shake detection parameters
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSlider, QSpinBox
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from gesture_detector import GestureDetector


class GestureTestWidget(QWidget):
    """Widget for testing gesture detection parameters"""
    
    def __init__(self):
        super().__init__()
        self.gesture_detector = None
        self.setup_ui()
        self.setup_gesture_detector()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("DropShelf Gesture Detection Test")
        self.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Gesture Detection Test")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # Instructions
        instructions = QLabel(
            "1. Start gesture detection\n"
            "2. Drag a file from File Explorer\n"
            "3. While dragging, shake mouse left-right quickly\n"
            "4. Adjust sensitivity if needed"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Detection")
        self.start_button.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Stop Detection")
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # Sensitivity controls
        sensitivity_layout = QVBoxLayout()
        
        # Shake threshold
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("Shake Threshold:"))
        self.threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.threshold_slider.setRange(20, 200)
        self.threshold_slider.setValue(80)
        self.threshold_slider.valueChanged.connect(self.update_threshold)
        threshold_layout.addWidget(self.threshold_slider)
        self.threshold_label = QLabel("80")
        threshold_layout.addWidget(self.threshold_label)
        sensitivity_layout.addLayout(threshold_layout)
        
        # Time window
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("Time Window (ms):"))
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setRange(100, 500)
        self.time_slider.setValue(200)
        self.time_slider.valueChanged.connect(self.update_time_window)
        time_layout.addWidget(self.time_slider)
        self.time_label = QLabel("200")
        time_layout.addWidget(self.time_label)
        sensitivity_layout.addLayout(time_layout)
        
        # Direction changes
        changes_layout = QHBoxLayout()
        changes_layout.addWidget(QLabel("Min Direction Changes:"))
        self.changes_slider = QSlider(Qt.Orientation.Horizontal)
        self.changes_slider.setRange(2, 8)
        self.changes_slider.setValue(3)
        self.changes_slider.valueChanged.connect(self.update_direction_changes)
        changes_layout.addWidget(self.changes_slider)
        self.changes_label = QLabel("3")
        changes_layout.addWidget(self.changes_label)
        sensitivity_layout.addLayout(changes_layout)
        
        layout.addLayout(sensitivity_layout)
        
        # Status display
        self.status_label = QLabel("Status: Ready")
        self.status_label.setStyleSheet("padding: 10px; background: #f0f0f0; border-radius: 5px;")
        layout.addWidget(self.status_label)
        
        # Detection log
        self.log_label = QLabel("Detection Log:")
        layout.addWidget(self.log_label)
        
    def setup_gesture_detector(self):
        """Setup the gesture detector"""
        self.gesture_detector = GestureDetector()
        self.gesture_detector.shake_detected.connect(self.on_shake_detected)
        self.gesture_detector.drag_started.connect(self.on_drag_started)
        self.gesture_detector.drag_ended.connect(self.on_drag_ended)
        
    def start_detection(self):
        """Start gesture detection"""
        if self.gesture_detector:
            self.gesture_detector.start_detection()
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Status: Detection Active - Drag files and shake mouse!")
            self.status_label.setStyleSheet("padding: 10px; background: #d4edda; border-radius: 5px; color: #155724;")
    
    def stop_detection(self):
        """Stop gesture detection"""
        if self.gesture_detector:
            self.gesture_detector.stop_detection()
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("Status: Detection Stopped")
            self.status_label.setStyleSheet("padding: 10px; background: #f8d7da; border-radius: 5px; color: #721c24;")
    
    def update_threshold(self, value):
        """Update shake threshold"""
        self.threshold_label.setText(str(value))
        if self.gesture_detector:
            self.gesture_detector.set_sensitivity(threshold=value)
    
    def update_time_window(self, value):
        """Update time window"""
        self.time_label.setText(str(value))
        if self.gesture_detector:
            self.gesture_detector.set_sensitivity(time_window=value)
    
    def update_direction_changes(self, value):
        """Update minimum direction changes"""
        self.changes_label.setText(str(value))
        if self.gesture_detector:
            self.gesture_detector.set_sensitivity(min_changes=value)
    
    def on_shake_detected(self, x, y):
        """Handle shake detection"""
        message = f"🎉 SHAKE DETECTED at ({x}, {y})!"
        self.log_label.setText(f"Detection Log:\n{message}")
        self.log_label.setStyleSheet("padding: 10px; background: #fff3cd; border-radius: 5px; color: #856404;")
        print(message)
    
    def on_drag_started(self):
        """Handle drag start"""
        message = "📁 Drag operation started"
        self.log_label.setText(f"Detection Log:\n{message}")
        print(message)
    
    def on_drag_ended(self):
        """Handle drag end"""
        message = "📁 Drag operation ended"
        self.log_label.setText(f"Detection Log:\n{message}")
        print(message)
    
    def closeEvent(self, event):
        """Handle window close"""
        if self.gesture_detector:
            self.gesture_detector.stop_detection()
        event.accept()


def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    widget = GestureTestWidget()
    widget.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
